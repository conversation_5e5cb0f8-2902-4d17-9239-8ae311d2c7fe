package to.sunset.litetools

import java.util.*

actual class TimerService actual constructor() {
    private var timer : Timer? = null

    actual fun start(intervalMillis: Long, action: () -> Unit) {
        stop()
        timer = Timer().apply {
            scheduleAtFixedRate(object : TimerTask() {
                override fun run() {
                    action()
                }
            }, intervalMillis, intervalMillis)
        }
    }

    actual fun stop() {
        timer?.cancel()
        timer = null
    }

    actual val isRunning: <PERSON>olean
        get() = timer != null
}