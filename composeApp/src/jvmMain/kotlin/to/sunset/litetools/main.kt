package to.sunset.litetools

import androidx.compose.ui.window.Tray
import androidx.compose.ui.window.Window
import androidx.compose.ui.window.application
import androidx.compose.ui.window.rememberTrayState
import androidx.compose.ui.window.rememberWindowState
import java.awt.Dimension
import litetools.composeapp.generated.resources.Res
import litetools.composeapp.generated.resources.icon
import org.jetbrains.compose.resources.painterResource

fun main() = application {
    val trayState = rememberTrayState()
    val windowState = rememberWindowState()
    var isVisible = true

    Tray(
        state = trayState,
        icon = painterResource(Res.drawable.icon),
        tooltip = "LiteTools",
        menu = {
            Item("显示", onClick = { isVisible = true })
            Item("退出", onClick = ::exitApplication)
        }
    )

    if (isVisible) {
        Window(
            onCloseRequest = { isVisible = false },
            state = windowState,
            title = "LiteTools",
            icon = painterResource(Res.drawable.icon),
        ) {
            App()
        }
    }
}