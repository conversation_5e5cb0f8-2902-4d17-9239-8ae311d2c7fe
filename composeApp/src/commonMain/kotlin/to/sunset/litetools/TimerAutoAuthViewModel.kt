package to.sunset.litetools

import androidx.lifecycle.ViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import java.util.*

/**
 * 自动认证器
 */
class TimerAutoAuthViewModel : ViewModel() {
    private val timerService: TimerService = TimerService()

    private var autoAuth: AutoAuth = AutoAuth()

    private val _isRunning = MutableStateFlow(false)

    val isRunning: StateFlow<Boolean> = _isRunning

    val pwd = MutableStateFlow("")


    val username = MutableStateFlow("")


    fun toggleTimer() {
        println("切换启动状态:" + Calendar.getInstance().time)
        if (isRunning.value) {
            stopTimer()
        } else {
            if (pwd.value.isEmpty() || username.value.isEmpty()) {
                return
            }
            startTimer()
        }
    }


    private fun startTimer() {
        timerService.start(30000) {
            println("定时任务启动:" + Calendar.getInstance().time)
            autoAuth.loop(username.value, pwd.value)
        }
        _isRunning.value = true
    }


    private fun stopTimer() {
        timerService.stop()
        _isRunning.value = false
    }


    override fun onCleared() {
        super.onCleared()
        stopTimer()
    }
}