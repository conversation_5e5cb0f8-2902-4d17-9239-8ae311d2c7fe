package to.sunset.litetools

import java.net.HttpURLConnection
import java.net.Inet4Address
import java.net.NetworkInterface
import java.net.URL
import java.util.*
import javax.net.ssl.SSLHandshakeException
import kotlin.concurrent.schedule

class AutoAuth {
     private val IP = "*********"
     private val SOMETHING_URL = "https://www.baidu.com"
     private val LOGIN_URL = "http://**********/ac_portal/login.php"
     private var authTime: Long = 0

    fun loop(username: String, password: String) {
        try {
            if (isTargetEnv()) {
                println("检测到目标环境,开始检测网络")
                if (!networkCheck()) {
                    println("网络异常,尝试重新登录")
                    if (login(username, password)) {
                        println("登录成功:" + Calendar.getInstance().time)
                    }
                } else {
                    println("网络正常:" + Calendar.getInstance().time)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


    fun networkCheck(): Boolean {
        if (authTime > 0L) {
            val difference = System.currentTimeMillis() - authTime
            // 如果时间差小于 6天.59.59
            if (difference < 7 * 24 * 60 * 60 * 1000 - 1000) {
                return true
            }
        }

        if (!getSomething()) {
            return false
        }
        return true
    }


    fun isTargetEnv(): Boolean {
        return IP in getIp()
    }

    fun getIp(): Set<String> {
        return NetworkInterface.getNetworkInterfaces().asSequence()
            .flatMap { it.inetAddresses.asSequence() }
            .filterIsInstance<Inet4Address>()
            .filterNot { it.isLoopbackAddress }
            .map { it.hostAddress }.toSet()
    }


    fun getSomething(): Boolean {
        try {
            val client = URL(SOMETHING_URL).openConnection() as HttpURLConnection
            client.connectTimeout = 3000
            var code = client.responseCode
            var message = client.responseMessage
            var responseBody = client.inputStream.bufferedReader().use { it.readText() }
            return responseBody.contains("百度一下")
        } catch (e: SSLHandshakeException) {
            //报ssl错误
            //说明要登录
            return false
        } catch (e: Exception) {
            //报错
            e.printStackTrace()
        }
        return false
    }


    fun login(username: String, password: String): Boolean {
        val time = System.currentTimeMillis()
        val pwd = doEncryptRC4(password, time.toString())
        val data = "opr=pwdLogin&userName=$username&pwd=$pwd&auth_tag=$time&rememberPwd=1"
        val url = URL(LOGIN_URL)
        val connection = url.openConnection() as HttpURLConnection
        connection.requestMethod = "POST"
        connection.doOutput = true
        connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded")
        connection.setRequestProperty("Content-Length", data.length.toString())
        connection.outputStream.use {
            it.write(data.toByteArray())
            it.flush()
        }
        val responseCode = connection.responseCode
        if (responseCode == 200) {
            authTime = time
            return true
        } else {
            return false
        }
    }



    fun doEncryptRC4(src: String, passwd: String): String {
        val trimmedSrc = src.trim()
        val key = IntArray(256)
        val sbox = IntArray(256)
        val output = mutableListOf<String>()
        val plen = passwd.length
        val size = trimmedSrc.length

        // 初始化 key 和 sbox
        for (i in 0 until 256) {
            key[i] = passwd[i % plen].code
            sbox[i] = i
        }

        // 打乱 sbox
        var j = 0
        for (i in 0 until 256) {
            j = (j + sbox[i] + key[i]) % 256
            // 交换 sbox[i] 和 sbox[j]
            val temp = sbox[i]
            sbox[i] = sbox[j]
            sbox[j] = temp
        }

        // 加密处理
        var a = 0
        var b = 0
        for (i in 0 until size) {
            a = (a + 1) % 256
            b = (b + sbox[a]) % 256

            // 交换 sbox[a] 和 sbox[b]
            val tempSwap = sbox[a]
            sbox[a] = sbox[b]
            sbox[b] = tempSwap

            val c = (sbox[a] + sbox[b]) % 256
            var charCode = trimmedSrc[i].code xor sbox[c]

            // 转换为十六进制字符串并确保两位格式
            var hex = charCode.toString(16)
            hex = when {
                hex.length == 1 -> "0$hex"
                hex.isEmpty() -> "00"
                else -> hex
            }
            output.add(hex)
        }
        return output.joinToString("")
    }
}