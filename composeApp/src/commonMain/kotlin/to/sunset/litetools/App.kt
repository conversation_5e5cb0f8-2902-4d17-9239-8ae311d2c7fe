package to.sunset.litetools

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.ui.tooling.preview.Preview

import litetools.composeapp.generated.resources.Res
import litetools.composeapp.generated.resources.compose_multiplatform

@Composable
@Preview
fun App() {
    val timerAutoAuthViewModel = remember { TimerAutoAuthViewModel() }
    val isTaskRunning by timerAutoAuthViewModel.isRunning.collectAsState()

    val pwd by timerAutoAuthViewModel.pwd.collectAsState()
    val username by timerAutoAuthViewModel.username.collectAsState()

    MaterialTheme {
        Row(
            modifier = Modifier
                .fillMaxSize(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(horizontalAlignment = Alignment.CenterHorizontally) { Text(text = "自动鉴权:") }
            OutlinedTextField(
                value = username,
                onValueChange = { timerAutoAuthViewModel.username.value = it },
                label = { Text("用户名") },
                modifier = Modifier
                    .weight(1f),
                singleLine = true,
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Text),
                enabled = !isTaskRunning
            )
            OutlinedTextField(
                value = pwd,
                onValueChange = { timerAutoAuthViewModel.pwd.value = it },
                label = { Text("密码") },
                modifier = Modifier
                    .weight(1f),
                singleLine = true,
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
                visualTransformation = PasswordVisualTransformation(),
                enabled = !isTaskRunning
            )
            Column(horizontalAlignment = Alignment.CenterHorizontally,verticalArrangement = Arrangement.Center) {
                Switch(checked = isTaskRunning, onCheckedChange = { timerAutoAuthViewModel.toggleTimer() })
                Text(text = if (isTaskRunning) "启用" else "停止")
            }
        }
    }
}